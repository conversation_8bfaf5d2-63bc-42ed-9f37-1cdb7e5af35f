from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>, Head<PERSON>, Request
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.config.database import get_db
from app.controllers.user_controller import UserController
from app.schemas.user_schema import Use<PERSON><PERSON><PERSON>, User, User<PERSON><PERSON><PERSON>, UserResponse
from app.services.outlook_service import OutlookService
from app.services.gmail_service import GmailService
from fastapi.responses import RedirectResponse
from passlib.context import CryptContext
from app.utils.security import decode_jwt_token
import re
from typing import Optional

router = APIRouter( tags=["users"])

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def validate_password(password: str) -> tuple[bool, Optional[str]]:
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r"[A-Z]", password):
        return False, "Password must contain at least one uppercase letter"
    if not re.search(r"[a-z]", password):
        return False, "Password must contain at least one lowercase letter"
    if not re.search(r"\d", password):
        return False, "Password must contain at least one number"
    if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
        return False, "Password must contain at least one special character"
    return True, None

@router.post("/users/register", response_model=UserResponse, status_code=201, 
            description="Register a new user with email configuration")
async def create_user(user: UserCreate, db: AsyncIOMotorDatabase = Depends(get_db)):
    return await UserController.create_user(user, db)

@router.get("/users", description="Get current user details")
async def get_user(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Get details of the currently authenticated user
    
    Returns:
        User details excluding sensitive information
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    token = authorization.replace("Bearer ", "")
    return await UserController.get_user(token, db)

@router.put("/users/update", description="Update user details and email configuration")
async def update_user(
    update_data: dict,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    """
    Update user details and email configuration flexibly
    
    Args:
        update_data: Dictionary containing fields to update
        Fields can include:
        - username
        - full_name
        - email_config: {
            email: str,
            password: str,
            host: str,
            port: int,
            email_provider: str
        }
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    token = authorization.replace("Bearer ", "")
    token = authorization.replace("Bearer ", "")
    user_id = decode_jwt_token(token)
    return await UserController.update_user(update_data, user_id, db)

@router.get("/users/outlook/auth", description="Start Microsoft OAuth flow")
async def outlook_auth(
):
    """
    Start Outlook OAuth flow by redirecting to Microsoft login page
    """
    # if not authorization:
    #     raise HTTPException(status_code=401, detail="Authorization header missing")
    # token = authorization.replace("Bearer ", "")
    
    outlook_service = OutlookService()
    auth_url = await outlook_service.get_auth_url()
    
    # Return URL for frontend to handle redirect
    return RedirectResponse(auth_url)

@router.get("/users/gmail/auth", description="Start Gmail OAuth flow")
async def gmail_auth(
    # db: AsyncIOMotorDatabase = Depends(get_db),
    # authorization: str = Header(None)
):
    """
    Start Gmail OAuth flow by redirecting to Google login page
    """
    # if not authorization:
    #     raise HTTPException(status_code=401, detail="Authorization header missing")
    # token = authorization.replace("Bearer ", "")
    # user_id = decode_jwt_token(token)
    
    gmail_service = GmailService()
    auth_url = await gmail_service.get_auth_url()
    
    # Return URL for frontend to handle redirect
    return RedirectResponse(auth_url)
    # return {"auth_url": auth_url}

@router.get("/users/outlook/callback", description="Handle Microsoft OAuth callback")
async def outlook_callback(
    request: Request,
    code: str,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Handle OAuth callback from Microsoft, exchange code for tokens
    """
    # if not authorization:
    #     raise HTTPException(status_code=401, detail="Authorization header missing")
    # user_id = authorization.replace("Bearer ", "")
    
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    outlook_service = OutlookService()
    return await outlook_service.handle_auth_callback(code, authorization=authorization)

@router.get("/users/gmail/callback", description="Handle Gmail OAuth callback")
async def gmail_callback(
    request: Request,
    code: str,
    # db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Handle Gmail OAuth callback from Google, exchange code for tokens
    """
    # if not authorization:
    #     raise HTTPException(status_code=401, detail="Authorization header missing")
    # user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
    
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    gmail_service = GmailService()
    return await gmail_service.handle_auth_callback(code, authorization=authorization)

@router.post("/users/login", description="Authenticate a user and return token")
async def login(user_credentials: UserLogin, db: AsyncIOMotorDatabase = Depends(get_db)):
    """
    Login endpoint to authenticate users
    
    Args:
        user_credentials: User login credentials (email and password)
        db: Database connection
        
    Returns:
        JWT token if authentication is successful
    
    Raises:
        HTTPException: If credentials are invalid or user is not found
    """
    return await UserController.login_user(user_credentials, db)

@router.get("/users/by-email/{email}", description="Get user details by email address")
async def get_user_by_email(
    email: str,
    db: AsyncIOMotorDatabase = Depends(get_db),
    # authorization: str = Header(None)
):
    """
    Get user details by searching for the provided email in either gmail_config or outlook_config
    
    Args:
        email: Email address to search for
        db: Database connection
        authorization: JWT token for authentication
        
    Returns:
        User details if found
        
    Raises:
        HTTPException: If user is not found or unauthorized
    """
    # if not authorization or not authorization.startswith("Bearer "):
    #     raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    # Verify the token is valid
    # try:
    #     token = authorization.replace("Bearer ", "")
    #     user_id = decode_jwt_token(token)
    # except Exception as e:
    #     raise HTTPException(status_code=401, detail="Invalid or expired token")
    
    # Search for users with the given email in either gmail_config or outlook_config
    # Sort by _id in descending order to get the most recently created user first
    cursor = db.users.find({
        "$or": [
            {"email_config.gmail_config.email": email},
            {"email_config.outlook_config.email": email}
        ]
    }).sort("_id", -1).limit(1)
    
    # Get the first (most recent) user
    user = await cursor.to_list(length=1)
    user = user[0] if user else None
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found with the provided email")
    
    # Convert ObjectId to string for JSON serialization
    if "_id" in user:
        user["_id"] = str(user["_id"])
    
    # Remove sensitive information before returning
    user.pop("password", None)
    if "email_config" in user:
        if "gmail_config" in user["email_config"] and "refresh_token" in user["email_config"]["gmail_config"]:
            user["email_config"]["gmail_config"].pop("refresh_token")
        if "outlook_config" in user["email_config"] and "refresh_token" in user["email_config"]["outlook_config"]:
            user["email_config"]["outlook_config"].pop("refresh_token")
    
    return user
