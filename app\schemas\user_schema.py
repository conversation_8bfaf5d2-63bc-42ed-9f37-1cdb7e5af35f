from pydantic import BaseModel, Field
from typing import Optional
from bson import ObjectId

class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v, handler):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema, **kwargs):
        field_schema.update(type="string")
        return field_schema

from pydantic import EmailStr, constr, BaseModel
from typing import Optional

class UserLogin(BaseModel):
    username: str
    password: str

class UserEmailConfig(BaseModel):
    email: EmailStr
    host: str
    port: int
    email_provider: str = "gmail"

class UserDetailsResponse(BaseModel):
    status_code: int = 200
    status: str = "OK"
    message: str = "User details fetched successfully"
    data: dict = {
        "username": str,
        "full_name": str,
        "email_config": UserEmailConfig
    }

class GmailConfig(BaseModel):
    email: EmailStr
    password: str
    host: str
    port: int

class OutlookConfig(BaseModel):
    email: EmailStr
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    expires_in: Optional[int] = None
    token_timestamp: Optional[float] = None

class EmailConfig(BaseModel):
    provider: str  # "gmail" or "outlook"
    gmail_config: Optional[GmailConfig] = None
    outlook_config: Optional[OutlookConfig] = None

class UserBase(BaseModel):
    username: constr(min_length=3, max_length=50)
    full_name: constr(min_length=2, max_length=100)

class UserCreate(UserBase):
    password: constr(min_length=8)
    email_config: Optional[EmailConfig] = None

    model_config = {
        "json_schema_extra": {
            "example": {
                "username": "johndoe1",
                "full_name": "John Doe",
                "password": "Password123!",
                "email_config": {
                    "provider": "gmail",
                    "gmail_config": {
                        "email": "<EMAIL>",
                        "password": "email_password",
                        "host": "smtp.gmail.com",
                        "port": 587
                    }
                }
            }
        }
    }

class User(UserBase):
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }

class UserResponse(BaseModel):
    status_code: int
    status: str
    message: str
    data: User

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True
    }
